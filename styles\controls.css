/* Estilos de controles de cantidad y botones */
.quantity-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin: 1rem 0;
}

.quantity-btn {
    background: var(--primary-color, #1a2332);
    color: white;
    border: 2px solid transparent;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    cursor: pointer;
    font-weight: bold;
    font-size: 1.1rem;
    box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.08));
    transition: var(--transition-base, all 0.3s ease);
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover {
    background: var(--primary-hover, #2c3e50);
    transform: scale(1.1);
    box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.quantity-btn:active {
    transform: scale(0.95);
}

.quantity-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 35, 50, 0.2);
}

.quantity-display {
    font-weight: 600;
}

.add-to-cart-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--secondary-color, #c0392b);
    color: white;
    border: 2px solid transparent;
    border-radius: var(--border-radius, 6px);
    cursor: pointer;
    transition: var(--transition-base, all 0.3s ease);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.08));
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
}

.add-to-cart-btn:hover {
    background: var(--secondary-hover, #e74c3c);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.add-to-cart-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.08));
}

.add-to-cart-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(192, 57, 43, 0.2);
}

.add-to-cart-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Estilo específico para el botón en la tabla */
.products-table .add-to-cart-btn {
    padding: 0.5rem;
    font-size: 0.9rem;
    margin-top: 0.5rem;
    width: 100%;
}

/* Hover ya definido arriba con mejor contraste */

/* Controles de vista */
.view-controls {
    max-width: 1400px;
    margin: 1rem auto;
    padding: 0 1rem;
    display: flex;
    gap: 1rem;
}

.view-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--primary-color);
    background: white;
    border-radius: 4px;
    cursor: pointer;
}

.view-btn.active {
    background: var(--primary-color);
    color: white;
}

/* Estilos para el botón de cotización - MEJORADO CON ALTO CONTRASTE */
.add-to-quote-btn {
    width: 100%;
    padding: 0.8rem;
    background: var(--info-color, #117a8b);
    color: white;
    border: 2px solid transparent;
    border-radius: var(--border-radius, 6px);
    cursor: pointer;
    transition: var(--transition-base, all 0.3s ease);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.08));
    margin-top: 0.5rem;
    position: relative;
    overflow: hidden;
}

.add-to-quote-btn:hover {
    background: var(--info-hover, #17a2b8);
    border-color: var(--info-hover, #17a2b8);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1));
}

.add-to-quote-btn:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.08));
}

.add-to-quote-btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(17, 122, 139, 0.2);
}

.add-to-quote-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Botones lado a lado en la vista de tarjetas */
.product-card .button-group {
    display: flex;
    gap: 0.5rem;
    padding: 0 1rem 1rem 1rem;
}

.product-card .button-group .add-to-cart-btn,
.product-card .button-group .add-to-quote-btn {
    flex: 1;
    margin-top: 0;
}

/* Botones en la tabla */
.products-table td:last-child {
    white-space: nowrap;
}

.products-table .add-to-cart-btn,
.products-table .add-to-quote-btn {
    display: inline-block;
    width: auto;
    padding: 0.5rem 0.8rem;
    font-size: 0.9rem;
    margin: 0 0.25rem;
}

.products-table .add-to-cart-btn:first-child {
    margin-left: 0;
}

.products-table .add-to-quote-btn:last-child {
    margin-right: 0;
}

/* Estilos para controles de descuento */
.discount-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.discount-type {
    width: 60px;
}

.discount-value {
    flex-grow: 1;
    width: 80px;
}

.checkout-form {
    padding: 1rem;
    border-top: 1px solid #eee;
}

.checkout-input {
    width: 100%;
    padding: 0.5rem;
    margin-bottom: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.checkout-btn {
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: background 0.3s ease;
}

.checkout-btn:hover {
    background: var(--hover-color);
}

.modern-input {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0.5rem;
}

.modern-select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 1rem;
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 0.5rem;
}

.client-section {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.client-section input {
    flex-grow: 2;
    height: 40px;
}

.client-section button {
    flex-shrink: 0;
    height: 40px;
    width: 50px;
}