/**
 * Script para corregir el problema con el botón openDTEBtn
 */
console.log("Inicializando script de corrección para el botón DTE");

// Función para verificar y corregir el botón DTE
function fixDTEButton() {
    console.log("Verificando elementos del formulario DTE...");
    
    // Verificar que existan los elementos necesarios
    const openDTEBtn = document.getElementById('openDTEBtn');
    const dteCanvas = document.getElementById('dteCanvas');
    const dteOverlay = document.getElementById('dteOverlay');
    const closeDTEBtn = document.getElementById('closeDTEBtn');
    
    console.log("Elementos encontrados:", {
        openDTEBtn: openDTEBtn ? "✅" : "❌",
        dteCanvas: dteCanvas ? "✅" : "❌",
        dteOverlay: dteOverlay ? "✅" : "❌",
        closeDTEBtn: closeDTEBtn ? "✅" : "❌"
    });
    
    // Si el botón existe, asignar el evento correctamente
    if (openDTEBtn) {
        console.log("Eliminando eventos previos del botón openDTEBtn");
        
        // Clonar el botón para eliminar todos los event listeners previos
        const newBtn = openDTEBtn.cloneNode(true);
        openDTEBtn.parentNode.replaceChild(newBtn, openDTEBtn);
        
        console.log("Asignando nuevo event listener al botón openDTEBtn");
        
        // Asignar el nuevo event listener
        newBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log("Botón openDTEBtn clickeado");
            
            if (dteCanvas && dteOverlay) {
                console.log("Abriendo canvas DTE");
                dteCanvas.classList.add('active');
                dteOverlay.classList.add('active');
                
                // Verificar si las funciones existen antes de llamarlas
                if (typeof fetchFolio === 'function') {
                    console.log("Llamando a fetchFolio()");
                    fetchFolio();
                } else {
                    console.error("La función fetchFolio no está definida");
                }
                
                if (typeof cargarAlmacenes === 'function') {
                    console.log("Llamando a cargarAlmacenes()");
                    cargarAlmacenes();
                } else {
                    console.error("La función cargarAlmacenes no está definida");
                }

                // Inicializar el receptor genérico para boletas
                if (typeof initializeDTECanvas === 'function') {
                    console.log("Llamando a initializeDTECanvas()");
                    initializeDTECanvas();
                } else {
                    console.error("La función initializeDTECanvas no está definida");
                }

                // Forzar receptor genérico para boletas
                setTimeout(() => {
                    if (typeof forceBoletaReceptorGenerico === 'function') {
                        console.log("Llamando a forceBoletaReceptorGenerico()");
                        forceBoletaReceptorGenerico();
                    }
                }, 200);
            } else {
                console.error("No se encontraron los elementos del canvas DTE");
                alert("Error: No se pudo abrir el formulario DTE. Faltan elementos en el DOM.");
            }
        });
    } else {
        console.error("No se encontró el botón openDTEBtn");
    }
    
    // Verificar y corregir el botón de cierre
    if (closeDTEBtn && dteCanvas && dteOverlay) {
        console.log("Eliminando eventos previos del botón closeDTEBtn");
        
        // Clonar el botón para eliminar todos los event listeners previos
        const newCloseBtn = closeDTEBtn.cloneNode(true);
        closeDTEBtn.parentNode.replaceChild(newCloseBtn, closeDTEBtn);
        
        console.log("Asignando nuevo event listener al botón closeDTEBtn");
        
        // Asignar el nuevo event listener
        newCloseBtn.addEventListener('click', function() {
            console.log("Botón closeDTEBtn clickeado");
            dteCanvas.classList.remove('active');
            dteOverlay.classList.remove('active');
        });
    }
    
    // Verificar y corregir el overlay
    if (dteOverlay && dteCanvas) {
        console.log("Eliminando eventos previos del overlay");
        
        // Clonar el overlay para eliminar todos los event listeners previos
        const newOverlay = dteOverlay.cloneNode(true);
        dteOverlay.parentNode.replaceChild(newOverlay, dteOverlay);
        
        console.log("Asignando nuevo event listener al overlay");
        
        // Asignar el nuevo event listener
        newOverlay.addEventListener('click', function() {
            console.log("Overlay clickeado");
            dteCanvas.classList.remove('active');
            this.classList.remove('active');
        });
    }
}

// Ejecutar la función cuando el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM cargado, ejecutando fixDTEButton()");
    // fixDTEButton(); // Comentado para depuración de quoteCanvas
    
    // Ensure the outside click listener is added only once
    /*
    if (!document.body.hasAttribute('data-outside-click-listener-added')) {
        document.addEventListener('click', function(event) {
            console.log('Outside click detected, target: ', event.target);
            const dteCanvas = document.getElementById('dteCanvas');
            
            // Excluir botones específicos que están dentro del canvas
            const isLimpiarBtn = event.target.id === 'limpiarFormBtn' || event.target.closest('#limpiarFormBtn');
            const isPrintPdfBtn = event.target.id === 'printPdfBtn' || event.target.closest('#printPdfBtn');
            const isCanvasBtn = event.target.closest('.json-btn') || event.target.closest('.modern-button');
            
            if (dteCanvas && !dteCanvas.contains(event.target) && dteCanvas.classList.contains('active') && !isLimpiarBtn && !isPrintPdfBtn && !isCanvasBtn) {
                // Add a short delay to allow the canvas to fully open before checking for outside clicks
                setTimeout(() => {
                    if (!dteCanvas.contains(event.target) && dteCanvas.classList.contains('active')) {
                        dteCanvas.classList.remove('active');
                        const dteOverlay = document.getElementById('dteOverlay');
                        if (dteOverlay) {
                            dteOverlay.classList.remove('active');
                        }
                    }
                }, 100);  // 100ms delay to debounce after open action
            }
        });
        document.body.setAttribute('data-outside-click-listener-added', 'true');
    }
    */
});

// También ejecutar la función después de un breve retraso para asegurar que todos los elementos estén cargados
setTimeout(function(){
    console.log("Ejecutando fixDTEButton() después de un retraso");
    // fixDTEButton(); // Comentado para depuración de quoteCanvas
}, 1000);
