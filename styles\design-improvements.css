/* ==========================================================================
   MEJORAS DE DISEÑO PROFESIONAL - OVERRIDES ESPECÍFICOS
   Este archivo aplica mejoras visuales sobre componentes existentes
   ========================================================================== */

/* ==========================================================================
   1. MEJORAS EN NOTIFICACIONES
   ========================================================================== */
.notification {
    border-radius: var(--border-radius-lg, 10px) !important;
    box-shadow: var(--shadow-lg, 0 10px 20px rgba(0, 0, 0, 0.15)) !important;
    backdrop-filter: blur(10px);
    border: none !important;
    font-weight: 500;
}

.notification.success {
    background: linear-gradient(135deg, rgba(212, 237, 218, 0.95) 0%, rgba(195, 230, 203, 0.95) 100%) !important;
    border-left: 5px solid var(--success-color, #1e7e34) !important;
    color: var(--success-color, #1e7e34) !important;
}

.notification.error {
    background: linear-gradient(135deg, rgba(248, 215, 218, 0.95) 0%, rgba(245, 198, 203, 0.95) 100%) !important;
    border-left: 5px solid var(--danger-color, #a02622) !important;
    color: var(--danger-color, #a02622) !important;
}

.notification.warning {
    background: linear-gradient(135deg, rgba(255, 243, 205, 0.95) 0%, rgba(255, 236, 179, 0.95) 100%) !important;
    border-left: 5px solid var(--warning-color, #d68910) !important;
    color: var(--warning-color, #d68910) !important;
}

.notification.info {
    background: linear-gradient(135deg, rgba(209, 236, 241, 0.95) 0%, rgba(184, 227, 235, 0.95) 100%) !important;
    border-left: 5px solid var(--info-color, #117a8b) !important;
    color: var(--info-color, #117a8b) !important;
}

/* ==========================================================================
   2. MEJORAS EN MODALES
   ========================================================================== */
.folios-modal,
.modal,
[class*="modal"] {
    border-radius: var(--border-radius-lg, 10px) !important;
    box-shadow: var(--shadow-xl, 0 20px 40px rgba(0, 0, 0, 0.2)) !important;
    border: 1px solid var(--border-color, #dee2e6) !important;
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.folios-modal-header,
.modal-header {
    border-bottom: 1px solid var(--border-color, #dee2e6) !important;
    padding-bottom: 1rem !important;
}

.folios-modal-header h2,
.modal-header h2,
.modal-title {
    color: var(--primary-color, #1a2332) !important;
    font-weight: 600 !important;
}

/* ==========================================================================
   3. MEJORAS EN FORMULARIOS DTE
   ========================================================================== */
.dte-form-section {
    background: var(--gray-100, #f8f9fa) !important;
    border: 1px solid var(--border-color, #dee2e6) !important;
    border-radius: var(--border-radius-lg, 10px) !important;
    padding: 1.5rem !important;
    margin-bottom: 1.5rem !important;
    box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.08)) !important;
}

.dte-form-section h3 {
    color: var(--primary-color, #1a2332) !important;
    font-weight: 600 !important;
    margin-bottom: 1rem !important;
    padding-bottom: 0.5rem !important;
    border-bottom: 2px solid var(--accent-color, #2471a3) !important;
}

/* ==========================================================================
   4. MEJORAS EN INPUTS Y SELECTS
   ========================================================================== */
.modern-input,
.modern-select,
input[type="text"],
input[type="number"],
input[type="email"],
input[type="date"],
select {
    border: 1px solid var(--border-color, #dee2e6) !important;
    border-radius: var(--border-radius, 6px) !important;
    padding: 0.625rem 0.875rem !important;
    transition: var(--transition-base, all 0.3s ease) !important;
    font-size: var(--font-size-base, 1rem) !important;
    background-color: var(--white, #ffffff) !important;
}

.modern-input:focus,
.modern-select:focus,
input:focus,
select:focus {
    border-color: var(--accent-color, #2471a3) !important;
    box-shadow: 0 0 0 3px rgba(36, 113, 163, 0.1) !important;
    outline: none !important;
}



/* ==========================================================================
   6. MEJORAS EN BADGES Y CONTADORES
   ========================================================================== */
.dte-count,
.quote-count,
.cart-count {
    background: var(--secondary-color, #c0392b) !important;
    box-shadow: 0 2px 6px rgba(192, 57, 43, 0.3) !important;
    font-weight: 600 !important;
    border: 2px solid var(--white, #ffffff) !important;
    width: 20px !important;
    height: 20px !important;
    line-height: 16px !important;
}

/* ==========================================================================
   7. MEJORAS EN TOOLTIPS
   ========================================================================== */
.tooltip,
[data-tooltip]::after {
    background: var(--gray-900, #212529) !important;
    color: var(--white, #ffffff) !important;
    border-radius: var(--border-radius, 6px) !important;
    font-size: var(--font-size-sm, 0.875rem) !important;
    padding: 0.5rem 0.75rem !important;
    box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1)) !important;
}

/* ==========================================================================
   8. MEJORAS EN IMÁGENES DE PRODUCTOS
   ========================================================================== */
.product-image,
.table-image {
    border-radius: var(--border-radius, 6px) !important;
    box-shadow: var(--shadow-sm, 0 1px 3px rgba(0, 0, 0, 0.08)) !important;
    transition: var(--transition-base, all 0.3s ease) !important;
    border: 1px solid var(--border-color, #dee2e6) !important;
}

.product-image:hover,
.table-image:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1)) !important;
}

/* ==========================================================================
   9. MEJORAS EN LOADING STATES
   ========================================================================== */
.loading-indicator {
    background-color: rgba(26, 35, 50, 0.9) !important;
    backdrop-filter: blur(10px) !important;
}

.spinner {
    border-color: var(--gray-300, #dee2e6) !important;
    border-top-color: var(--accent-color, #2471a3) !important;
}

/* ==========================================================================
   10. MEJORAS EN SECCIONES DE INFORMACIÓN
   ========================================================================== */
.info-section,
.price-info,
.product-info {
    background: var(--gray-100, #f8f9fa) !important;
    padding: 0.75rem 1rem !important;
    border-radius: var(--border-radius, 6px) !important;
    border-left: 4px solid var(--info-color, #117a8b) !important;
    font-size: var(--font-size-sm, 0.875rem) !important;
    color: var(--gray-700, #495057) !important;
}

/* ==========================================================================
   11. MEJORAS EN BOTONES DE ACCIÓN
   ========================================================================== */
.action-button,
.action-btn {
    padding: 0.625rem 1.25rem !important;
    border-radius: var(--border-radius, 6px) !important;
    font-weight: 500 !important;
    transition: var(--transition-base, all 0.3s ease) !important;
    border: 2px solid transparent !important;
}

.action-button.primary,
.action-btn.primary {
    background: var(--accent-color, #2471a3) !important;
    color: var(--white, #ffffff) !important;
}

.action-button.primary:hover,
.action-btn.primary:hover {
    background: var(--accent-hover, #3498db) !important;
    transform: translateY(-1px) !important;
    box-shadow: var(--shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1)) !important;
}

/* ==========================================================================
   12. RESPONSIVE IMPROVEMENTS
   ========================================================================== */


/* ==========================================================================
   13. ANIMACIONES MEJORADAS
   ========================================================================== */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ==========================================================================
   14. ACCESIBILIDAD MEJORADA
   ========================================================================== */
:focus-visible {
    outline: 3px solid var(--accent-color, #2471a3) !important;
    outline-offset: 2px !important;
}

/* Soporte para modo de alto contraste */
@media (prefers-contrast: high) {
    .btn,
    button {
        border-width: 2px !important;
    }
    
    .notification {
        border-width: 3px !important;
    }
}

/* Soporte para movimiento reducido */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ==========================================================================
   FIN DE MEJORAS DE DISEÑO
   ========================================================================== */