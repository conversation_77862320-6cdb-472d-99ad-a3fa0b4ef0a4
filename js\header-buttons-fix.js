/**
 * Script para corregir los problemas con los botones del header
 * Este script asegura que las funciones globales necesarias estén disponibles
 */
console.log('Inicializando script de corrección para los botones del header');

// Función para corregir problemas de los botones del header
function fixHeaderButtons() {
    console.log('Verificando y corrigiendo botones del header...');
    
    // ===== CORRECCIÓN BOTÓN DTE =====
    const openDTEBtn = document.getElementById('openDTEBtn');
    const dteCanvas = document.getElementById('dteCanvas');
    const dteOverlay = document.getElementById('dteOverlay');
    
    console.log('Elementos DTE:', {
        openDTEBtn: openDTEBtn ? '✅' : '❌',
        dteCanvas: dteCanvas ? '✅' : '❌',
        dteOverlay: dteOverlay ? '✅' : '❌'
    });
    
    // Definir funciones faltantes para el DTE si no existen
    if (typeof window.fetchFolio !== 'function') {
        console.log('Definiendo función fetchFolio como stub');
        window.fetchFolio = function() {
            console.log('fetchFolio stub: Esta función debe ser implementada correctamente');
            // Aquí se debería implementar la lógica real o cargar dinámicamente el script que la contiene
        };
    }
    
    if (typeof window.cargarAlmacenes !== 'function') {
        console.log('Definiendo función cargarAlmacenes como stub');
        window.cargarAlmacenes = function() {
            console.log('cargarAlmacenes stub: Esta función debe ser implementada correctamente');
            // Aquí se debería implementar la lógica real o cargar dinámicamente el script que la contiene
        };
    }
    
    if (typeof window.initializeDTECanvas !== 'function') {
        console.log('Definiendo función initializeDTECanvas como stub');
        window.initializeDTECanvas = function() {
            console.log('initializeDTECanvas stub: Esta función debe ser implementada correctamente');
            // Aquí se debería implementar la lógica real o cargar dinámicamente el script que la contiene
        };
    }
    
    if (typeof window.forceBoletaReceptorGenerico !== 'function') {
        console.log('Definiendo función forceBoletaReceptorGenerico como stub');
        window.forceBoletaReceptorGenerico = function() {
            console.log('forceBoletaReceptorGenerico stub: Esta función debe ser implementada correctamente');
            // Aquí se debería implementar la lógica real o cargar dinámicamente el script que la contiene
        };
    }
    
    // ===== CORRECCIÓN BOTÓN COTIZACIÓN =====
    const openQuoteBtn = document.getElementById('openQuoteBtn');
    const quoteCanvas = document.getElementById('quoteCanvas');
    const quoteOverlay = document.getElementById('quoteOverlay');
    
    console.log('Elementos Cotización:', {
        openQuoteBtn: openQuoteBtn ? '✅' : '❌',
        quoteCanvas: quoteCanvas ? '✅' : '❌',
        quoteOverlay: quoteOverlay ? '✅' : '❌'
    });
    
    // Implementar función openQuoteCanvas si no existe
    if (typeof window.openQuoteCanvas !== 'function') {
        console.log('Definiendo función openQuoteCanvas globalmente');
        window.openQuoteCanvas = function() {
            console.log('Ejecutando openQuoteCanvas global');
            if (quoteCanvas && quoteOverlay) {
                quoteCanvas.classList.add('active');
                quoteOverlay.classList.add('active');
                console.log('Canvas de cotización activado');
                
                // Inicializar el canvas en el nivel correcto
                if (typeof window.navigateToLevel === 'function') {
                    window.navigateToLevel(1);
                }
            } else {
                console.error('Canvas o overlay no encontrado', {quoteCanvas, quoteOverlay});
            }
        };
    }
    
    // Implementar función closeQuoteCanvas si no existe
    if (typeof window.closeQuoteCanvas !== 'function') {
        console.log('Definiendo función closeQuoteCanvas globalmente');
        window.closeQuoteCanvas = function() {
            console.log('Ejecutando closeQuoteCanvas global');
            if (quoteCanvas && quoteOverlay) {
                quoteCanvas.classList.remove('active');
                quoteOverlay.classList.remove('active');
                console.log('Canvas de cotización desactivado');
                
                // Volver al primer nivel cuando se cierra
                setTimeout(() => {
                    if (typeof window.navigateToLevel === 'function') {
                        window.navigateToLevel(1);
                    }
                }, 300);
            }
        };
    }
    
    // Asegurar que el botón de cotización tenga un onclick correcto
    if (openQuoteBtn) {
        console.log('Actualizando evento onclick para el botón de cotización');
        openQuoteBtn.onclick = function(e) {
            e.preventDefault();
            e.stopPropagation();
            console.log('Click en botón de cotización');
            if (typeof window.openQuoteCanvas === 'function') {
                window.openQuoteCanvas();
            } else {
                console.error('La función openQuoteCanvas no está disponible');
                // Fallback directo
                if (quoteCanvas && quoteOverlay) {
                    quoteCanvas.classList.add('active');
                    quoteOverlay.classList.add('active');
                }
            }
            return false;
        };
    }
}

// Ejecutar la función cuando el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM cargado, ejecutando fixHeaderButtons()');
    fixHeaderButtons();
    
    // Ejecutar nuevamente después de un retraso para asegurar que todos los scripts se hayan cargado
    setTimeout(fixHeaderButtons, 1000);
    setTimeout(fixHeaderButtons, 2000); // Segunda verificación para asegurar la disponibilidad de las funciones
});

// Ejecutar inmediatamente por si el DOM ya está cargado
console.log('Ejecutando fixHeaderButtons() inmediatamente');
fixHeaderButtons();
