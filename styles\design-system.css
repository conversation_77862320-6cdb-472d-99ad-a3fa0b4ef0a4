/* ==========================================================================
   SISTEMA DE DISEÑO PROFESIONAL - TATA REPUESTOS
   Archivo maestro para unificar y mejorar el diseño visual
   ========================================================================== */

/* ==========================================================================
   1. VARIABLES DE DISEÑO
   ========================================================================== */
:root {
  /* Colores Corporativos Principales - Mejorados para accesibilidad */
  --primary-color: #1a2332;        /* Azul oscuro más profundo (mejor contraste) */
  --primary-hover: #2c3e50;        /* Color original como hover */
  --primary-light: #34495e;        /* Variante clara */
  
  --secondary-color: #c0392b;      /* Rojo más oscuro (mejor contraste) */
  --secondary-hover: #e74c3c;      /* Color original como hover */
  --secondary-light: #ec7063;      /* Variante clara */
  
  --accent-color: #2471a3;         /* Azul más oscuro (WCAG AA) */
  --accent-hover: #3498db;         /* Color original como hover */
  --accent-light: #5dade2;         /* Variante clara */
  
  --success-color: #1e7e34;        /* Verde más oscuro */
  --success-hover: #28a745;        /* Verde original como hover */
  --success-light: #5cb85c;        
  
  --warning-color: #d68910;        /* Naranja más oscuro */
  --warning-hover: #e67e22;        /* Color original como hover */
  --warning-light: #f39c12;
  
  --danger-color: #a02622;         /* Rojo oscuro para errores */
  --danger-hover: #dc3545;         
  --danger-light: #e57373;
  
  --info-color: #117a8b;           /* Cyan más oscuro */
  --info-hover: #17a2b8;
  --info-light: #5bc0de;
  
  /* Grises y Neutros */
  --gray-900: #212529;
  --gray-800: #343a40;
  --gray-700: #495057;
  --gray-600: #6c757d;
  --gray-500: #95a5a6;
  --gray-400: #ced4da;
  --gray-300: #dee2e6;
  --gray-200: #e9ecef;
  --gray-100: #f8f9fa;
  --white: #ffffff;
  
  /* Fondos */
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --bg-dark: #1a2332;
  --bg-light: #fafbfc;
  --bg-overlay: rgba(0, 0, 0, 0.6);
  
  /* Textos */
  --text-primary: #212529;
  --text-secondary: #495057;
  --text-muted: #6c757d;
  --text-light: #ffffff;
  --text-dark: #000000;
  
  /* Bordes */
  --border-color: #dee2e6;
  --border-color-dark: #ced4da;
  --border-radius: 6px;
  --border-radius-lg: 10px;
  --border-radius-sm: 4px;
  
  /* Sombras */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 20px rgba(0, 0, 0, 0.15);
  --shadow-xl: 0 20px 40px rgba(0, 0, 0, 0.2);
  
  /* Espaciado */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  
  /* Tipografía */
  --font-family-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  
  /* Transiciones */
  --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* ==========================================================================
   2. RESET Y BASE
   ========================================================================== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ==========================================================================
   3. TIPOGRAFÍA
   ========================================================================== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--spacing-md);
  color: var(--gray-900);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

.text-muted { color: var(--text-muted) !important; }
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

/* ==========================================================================
   4. BOTONES - Sistema unificado con alto contraste
   ========================================================================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: 0.625rem 1.25rem;
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  background-color: transparent;
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  transition: var(--transition-base);
  position: relative;
  overflow: hidden;
}

/* Estados de botón */
.btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(26, 35, 50, 0.2);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* Variantes de botón con alto contraste */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--white);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: var(--white);
}

.btn-secondary:hover {
  background-color: var(--secondary-hover);
  border-color: var(--secondary-hover);
}

.btn-accent {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: var(--white);
}

.btn-accent:hover {
  background-color: var(--accent-hover);
  border-color: var(--accent-hover);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: var(--white);
}

.btn-success:hover {
  background-color: var(--success-hover);
  border-color: var(--success-hover);
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
  color: var(--white);
}

.btn-danger:hover {
  background-color: var(--danger-hover);
  border-color: var(--danger-hover);
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: var(--white);
}

.btn-warning:hover {
  background-color: var(--warning-hover);
  border-color: var(--warning-hover);
}

.btn-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
  color: var(--white);
}

.btn-info:hover {
  background-color: var(--info-hover);
  border-color: var(--info-hover);
}

/* Botones outline */
.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

/* Tamaños de botón */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: var(--font-size-lg);
}

/* Botón de icono */
.btn-icon {
  width: 40px;
  height: 40px;
  padding: 0;
  border-radius: 50%;
}

.btn-icon.btn-sm {
  width: 32px;
  height: 32px;
}

.btn-icon.btn-lg {
  width: 48px;
  height: 48px;
}

/* ==========================================================================
   5. FORMULARIOS
   ========================================================================== */
.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--gray-700);
  font-size: var(--font-size-sm);
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.625rem 0.875rem;
  font-size: var(--font-size-base);
  font-weight: 400;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  transition: var(--transition-base);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-control:focus {
  color: var(--text-primary);
  background-color: var(--white);
  border-color: var(--accent-color);
  outline: 0;
  box-shadow: 0 0 0 3px rgba(36, 113, 163, 0.1);
}

.form-control:disabled {
  background-color: var(--gray-100);
  opacity: 0.8;
  cursor: not-allowed;
}

.form-control::placeholder {
  color: var(--gray-500);
  opacity: 1;
}

/* Select mejorado */
.form-select {
  padding-right: 2.5rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
}

/* Checkbox y Radio mejorados */
.form-check {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.form-check-input {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: var(--spacing-sm);
  border: 2px solid var(--border-color-dark);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
  cursor: pointer;
}

.form-check-input:checked {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

.form-check-input:focus {
  box-shadow: 0 0 0 3px rgba(36, 113, 163, 0.2);
}

/* ==========================================================================
   6. CARDS Y CONTENEDORES
   ========================================================================== */
.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--white);
  background-clip: border-box;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--spacing-lg);
  margin-bottom: 0;
  background-color: var(--gray-100);
  border-bottom: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
}

.card-body {
  flex: 1 1 auto;
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  background-color: var(--gray-100);
  border-top: 1px solid var(--border-color);
  border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}

/* ==========================================================================
   7. TABLAS PROFESIONALES
   ========================================================================== */
.table {
  width: 100%;
  margin-bottom: var(--spacing-md);
  background-color: var(--white);
  border-collapse: collapse;
}

.table thead th {
  padding: var(--spacing-md);
  text-align: left;
  font-weight: 600;
  color: var(--gray-700);
  background-color: var(--gray-100);
  border-bottom: 2px solid var(--border-color);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody td {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  color: var(--text-primary);
}

.table tbody tr:hover {
  background-color: var(--gray-50);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* ==========================================================================
   8. MODALES Y OVERLAYS
   ========================================================================== */
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--bg-overlay);
  z-index: var(--z-modal-backdrop);
  backdrop-filter: blur(4px);
  transition: opacity 0.3s ease;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  max-width: 90%;
  max-height: 90vh;
  overflow: auto;
  background-color: var(--white);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-xl);
  z-index: var(--z-modal);
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translate(-50%, -60%);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--gray-900);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

/* ==========================================================================
   9. ALERTAS Y NOTIFICACIONES
   ========================================================================== */
.alert {
  position: relative;
  padding: var(--spacing-md) var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  font-size: var(--font-size-base);
}

.alert-success {
  color: #0a3622;
  background-color: #d1e7dd;
  border-color: #a3cfbb;
}

.alert-danger {
  color: #58151c;
  background-color: #f8d7da;
  border-color: #f1aeb5;
}

.alert-warning {
  color: #664d03;
  background-color: #fff3cd;
  border-color: #ffe69c;
}

.alert-info {
  color: #055160;
  background-color: #cff4fc;
  border-color: #9eeaf9;
}

/* Notificación toast */
.toast {
  position: fixed;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  min-width: 300px;
  max-width: 500px;
  padding: var(--spacing-md);
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-popover);
  animation: toastSlideIn 0.3s ease;
}

@keyframes toastSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* ==========================================================================
   10. UTILIDADES
   ========================================================================== */

/* Espaciado */
.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xs) !important; }
.mt-2 { margin-top: var(--spacing-sm) !important; }
.mt-3 { margin-top: var(--spacing-md) !important; }
.mt-4 { margin-top: var(--spacing-lg) !important; }
.mt-5 { margin-top: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xs) !important; }
.mb-2 { margin-bottom: var(--spacing-sm) !important; }
.mb-3 { margin-bottom: var(--spacing-md) !important; }
.mb-4 { margin-bottom: var(--spacing-lg) !important; }
.mb-5 { margin-bottom: var(--spacing-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xs) !important; }
.p-2 { padding: var(--spacing-sm) !important; }
.p-3 { padding: var(--spacing-md) !important; }
.p-4 { padding: var(--spacing-lg) !important; }
.p-5 { padding: var(--spacing-xl) !important; }

/* Flexbox */
.d-flex { display: flex !important; }
.flex-column { flex-direction: column !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-center { justify-content: center !important; }
.align-items-center { align-items: center !important; }
.gap-1 { gap: var(--spacing-xs) !important; }
.gap-2 { gap: var(--spacing-sm) !important; }
.gap-3 { gap: var(--spacing-md) !important; }

/* Sombras */
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* Bordes */
.border { border: 1px solid var(--border-color) !important; }
.border-0 { border: 0 !important; }
.rounded { border-radius: var(--border-radius) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-circle { border-radius: 50% !important; }

/* ==========================================================================
   11. RESPONSIVE Y ACCESIBILIDAD
   ========================================================================== */

/* Preferencia de movimiento reducido */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Modo oscuro (preparado para futuro) */
@media (prefers-color-scheme: dark) {
  /* Las variables pueden ser sobrescritas aquí para modo oscuro */
}

/* Responsive breakpoints */
@media (max-width: 576px) {
  .modal {
    max-width: 95%;
  }
  
  .toast {
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .btn {
    padding: 0.5rem 1rem;
    font-size: var(--font-size-sm);
  }
  
  .card {
    border-radius: var(--border-radius);
  }
}

/* ==========================================================================
   12. ANIMACIONES PROFESIONALES
   ========================================================================== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.animate-fadeIn { animation: fadeIn 0.3s ease; }
.animate-slideUp { animation: slideUp 0.3s ease; }
.animate-pulse { animation: pulse 2s infinite; }

/* ==========================================================================
   13. MEJORAS ESPECÍFICAS PARA COMPONENTES EXISTENTES
   ========================================================================== */

/* Mejora para el header */
header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  box-shadow: var(--shadow-md);
}

/* Mejora para botones de búsqueda */
.search-button {
  background-color: var(--accent-color) !important;
  color: var(--white) !important;
}

.search-button:hover {
  background-color: var(--accent-hover) !important;
}

/* Mejora para botón reset */
.reset-button {
  background-color: var(--gray-600) !important;
  color: var(--white) !important;
}

.reset-button:hover {
  background-color: var(--gray-700) !important;
}

/* Mejora para botones de cantidad */
.quantity-btn {
  background-color: var(--primary-color) !important;
  color: var(--white) !important;
  border: none !important;
}

.quantity-btn:hover {
  background-color: var(--primary-hover) !important;
}

/* Mejora para productos cards */
.product-card {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-base);
}

.product-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}



/* Mejora para notificaciones */
.notification {
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
}

/* ==========================================================================
   FIN DEL SISTEMA DE DISEÑO
   ========================================================================== */